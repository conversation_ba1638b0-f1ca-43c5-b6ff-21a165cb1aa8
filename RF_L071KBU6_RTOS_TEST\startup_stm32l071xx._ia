--cpu Cortex-M0+ -g --apcs=interwork --pd "__MICROLIB SETA 1" -I ../Core/Inc
-I.\RTE\_RF_L071KBU6_RTOS_TEST
-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.4.0\CMSIS\Core\Include
-IC:\Keil_v5\ARM\PACK\Keil\STM32L0xx_DFP\2.1.0\Drivers\CMSIS\Device\ST\STM32L0xx\Include
--pd "__UVISION_VERSION SETA 529" --pd "_RTE_ SETA 1" --pd "STM32L071xx SETA 1"
--list startup_stm32l071xx.lst --xref -o rf_l071kbu6_rtos_test\startup_stm32l071xx.o --depend rf_l071kbu6_rtos_test\startup_stm32l071xx.d "startup_stm32l071xx.s"