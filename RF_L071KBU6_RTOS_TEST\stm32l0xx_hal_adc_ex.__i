--c99 -c --cpu Cortex-M0+ -D__MICROLIB -g -O3 --apcs=interwork --split_sections -I ../Core/Inc -I ../Drivers/STM32L0xx_HAL_Driver/Inc -I ../Drivers/STM32L0xx_HAL_Driver/Inc/Legacy -I ../Middlewares/Third_Party/FreeRTOS/Source/include -I ../Middlewares/Third_Party/FreeRTOS/Source/CMSIS_RTOS -I ../Middlewares/Third_Party/FreeRTOS/Source/portable/RVDS/ARM_CM0 -I ../Drivers/CMSIS/Device/ST/STM32L0xx/Include -I ../Drivers/CMSIS/Include
-I.\RTE\_RF_L071KBU6_RTOS_TEST
-IC:\Keil_v5\ARM\PACK\ARM\CMSIS\5.4.0\CMSIS\Core\Include
-IC:\Keil_v5\ARM\PACK\Keil\STM32L0xx_DFP\2.1.0\Drivers\CMSIS\Device\ST\STM32L0xx\Include
-D__UVISION_VERSION="529" -D_RTE_ -DSTM32L071xx -DUSE_HAL_DRIVER -DSTM32L071xx
-o rf_l071kbu6_rtos_test\stm32l0xx_hal_adc_ex.o --omf_browse rf_l071kbu6_rtos_test\stm32l0xx_hal_adc_ex.crf --depend rf_l071kbu6_rtos_test\stm32l0xx_hal_adc_ex.d "../Drivers/STM32L0xx_HAL_Driver/Src/stm32l0xx_hal_adc_ex.c"